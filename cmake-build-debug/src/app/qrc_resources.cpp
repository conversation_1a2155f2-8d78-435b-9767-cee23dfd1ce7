/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.15.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // D:/Workspace/ailayout/src/resources/style.qss
  0x0,0x0,0x2,0xb8,
  0x0,
  0x0,0x9,0xcf,0x78,0x9c,0xad,0x56,0x5b,0x6b,0x13,0x41,0x14,0x7e,0xf,0xe4,0x3f,
  0x2c,0xe4,0xad,0xb8,0xf4,0x96,0x94,0x32,0x79,0x93,0xa2,0x2f,0xf5,0xa1,0x28,0xf6,
  0x79,0x76,0x67,0x92,0x1d,0x3a,0x99,0x59,0x66,0x27,0xb6,0x54,0x4,0x8b,0x58,0xb0,
  0x54,0x2,0x82,0x14,0xb5,0x81,0x54,0x1f,0x14,0x41,0xf3,0x20,0x78,0xa1,0xea,0xaf,
  0x71,0xb7,0xe9,0xbf,0x70,0xb6,0xd9,0x7b,0xf6,0x12,0xb4,0xbb,0x2f,0xcb,0x9c,0x9d,
  0x73,0xbe,0xf3,0x7d,0xe7,0x9c,0x99,0xc5,0x5,0xed,0xcf,0xf7,0xf3,0x8b,0x8f,0x27,
  0xee,0xe0,0xdd,0xe4,0xc9,0xb1,0xf7,0x6a,0xac,0x2d,0x2c,0xd6,0x6b,0x5b,0x77,0x20,
  0x61,0xdb,0x84,0x21,0xbe,0xab,0x3d,0xac,0xd7,0x34,0xf5,0x18,0xd0,0xdc,0xe9,0xa,
  0xde,0x67,0x48,0x37,0x39,0xe5,0x2,0x68,0x8d,0x15,0xc3,0x7f,0xdb,0xf5,0xda,0xa3,
  0x7a,0xad,0x5e,0x5b,0x5c,0xd0,0x26,0x83,0x53,0xf7,0xf9,0x4b,0x6f,0x34,0xf0,0x46,
  0xdf,0xdc,0x9f,0x83,0xc0,0x15,0x66,0xfd,0x9b,0x50,0x94,0xf8,0x59,0x35,0x57,0x3b,
  0xcd,0xe5,0xf6,0xd4,0x1e,0x2e,0x76,0xae,0x9e,0x60,0xd1,0xe0,0x2,0x61,0xa1,0x1b,
  0x5c,0x4a,0xde,0x3,0xda,0xb2,0xbd,0xa7,0x39,0x9c,0x12,0xa4,0x35,0x5a,0x57,0x4f,
  0x88,0x21,0xc,0x6,0x0,0x91,0xb8,0x37,0x1b,0x12,0x68,0x52,0x40,0xe6,0xd8,0x50,
  0x60,0x26,0x3,0xdf,0x36,0x44,0x88,0xb0,0x2e,0xd0,0x9a,0xca,0xeb,0xba,0xbd,0x97,
  0xef,0xb,0x38,0x98,0x62,0x53,0x62,0x94,0xe7,0xb4,0xd1,0x34,0xd6,0x30,0xec,0x14,
  0xec,0xb4,0x5,0x76,0x9c,0x82,0x8d,0xab,0xb0,0x5,0xd7,0x61,0xe,0x85,0x69,0xfe,
  0xae,0x81,0xbc,0x2a,0xd6,0xd2,0x94,0xa5,0x48,0x59,0x59,0xca,0xb0,0x52,0x49,0x49,
  0x84,0x2e,0x87,0x18,0xa0,0xf6,0x29,0x1,0xa0,0xe4,0x51,0x4d,0x58,0x98,0x74,0x2d,
  0x79,0x85,0xb0,0x9d,0x43,0x52,0x1a,0xad,0x22,0xe9,0xe2,0xe8,0xab,0xf7,0xf8,0x20,
  0x53,0x67,0x77,0x25,0x94,0x7d,0xe7,0x9a,0x2a,0x4d,0x72,0xbb,0x8c,0xb0,0x38,0xd6,
  0xd6,0x26,0x34,0x30,0x9d,0xa1,0x6d,0x25,0x66,0x4c,0xe1,0x75,0xf,0x4e,0x2f,0x87,
  0xa3,0x69,0x97,0x25,0x21,0x6f,0x70,0x73,0x67,0x9b,0xa0,0x2e,0x96,0x11,0xe6,0x4a,
  0xad,0xe2,0x3d,0x0,0x48,0x22,0x29,0xce,0xaf,0xab,0x64,0xa2,0x49,0x31,0xe7,0x6f,
  0x28,0xff,0xb7,0xe,0x67,0x52,0xdf,0xd,0xd4,0x31,0x38,0x45,0x45,0xd4,0xe5,0x65,
  0xea,0x3e,0xfd,0x72,0x79,0xf2,0xc9,0x3b,0x7e,0x76,0xf9,0xe2,0x73,0x36,0x5f,0x0,
  0x4c,0xca,0x1d,0xac,0x1b,0x7d,0x5,0x80,0xdd,0xd0,0x52,0xa6,0xe,0xe5,0x50,0x6,
  0xa6,0xf9,0x7a,0x38,0xa4,0x8d,0x71,0x86,0x83,0x25,0x62,0x72,0xa6,0x3b,0x64,0x1f,
  0xab,0xec,0xd6,0x12,0xf5,0x5b,0x84,0x1,0x58,0xfc,0x1,0x16,0x25,0x48,0xa6,0x3f,
  0x94,0xb7,0x7f,0x82,0x5a,0x1,0x11,0xe9,0x3b,0xd9,0x4a,0xf0,0xce,0x5f,0xbb,0x47,
  0x1f,0xbc,0xe1,0x59,0xaa,0x72,0x4d,0xc1,0x29,0xf5,0x7,0x86,0xa,0x20,0x89,0x9,
  0x69,0xb5,0xa6,0xbb,0x4,0x49,0x4b,0xa5,0xd6,0xca,0x4a,0x1a,0xc6,0x6d,0x26,0x72,
  0x8e,0x3,0x0,0xb,0x32,0x44,0x71,0x79,0xa0,0x16,0x6c,0xa1,0x16,0x2e,0x73,0xeb,
  0x1b,0x7a,0x84,0xe9,0x61,0xe3,0xa6,0x26,0x44,0x71,0xb4,0x79,0x28,0x9c,0xf1,0xa1,
  0x8a,0x57,0xa7,0x84,0xc5,0x5e,0x94,0x46,0x9,0xb3,0xd3,0x37,0xd2,0xe6,0xec,0x50,
  0x59,0xca,0x1d,0x2a,0x41,0xa5,0x64,0xa3,0x59,0x5c,0x90,0x7d,0x55,0xf4,0xf3,0x48,
  0x10,0x4d,0xad,0x7f,0xd4,0xa0,0x22,0xd6,0xfc,0x2a,0x4,0xb5,0x50,0x21,0x42,0x1c,
  0xee,0xff,0x64,0x88,0xfd,0x14,0x8,0x31,0x9b,0x57,0x0,0xb0,0x5a,0x9,0x7f,0x82,
  0xbc,0xf9,0xed,0xfe,0x7a,0x3b,0x79,0x7f,0xa8,0x3e,0x92,0x37,0x92,0xdb,0x2,0xda,
  0x16,0x31,0x9d,0xfb,0x4,0x97,0xdd,0x49,0x96,0xb1,0xff,0xe6,0x8f,0x85,0x28,0xc4,
  0xf8,0xc7,0x64,0x3c,0xf4,0xce,0xe,0x93,0x4d,0x78,0x8b,0x50,0xbc,0x41,0x20,0xe5,
  0xdd,0xb9,0x6f,0x3c,0xc9,0x3d,0x5b,0x9b,0xc4,0x91,0x3e,0x36,0x45,0x4a,0x72,0xf9,
  0x9e,0xc0,0xb8,0x2,0x72,0xe4,0xb4,0xf0,0x50,0x9a,0x1e,0xb4,0x44,0xd,0xb4,0x8a,
  0x23,0xf6,0x2f,0xa2,0xdc,0x1f,0x1a,
  
};

static const unsigned char qt_resource_name[] = {
  // style.qss
  0x0,0x9,
  0x0,0x28,0xad,0x23,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/style.qss
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x99,0x52,0x76,0x2f,0x8c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}
