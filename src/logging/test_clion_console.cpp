#include "logging/logger.h"
#include <iostream>
#include <thread>
#include <chrono>

/**
 * @brief 专门测试 CLion 控制台输出的程序
 * 不会弹出新的控制台窗口，所有输出都在 CLion 的 Run 窗口中显示
 */

void TestBasicOutput() {
    std::cout << "\n=== Testing Basic Console Output in CLion ===" << std::endl;
    
    // 测试标准输出
    std::cout << "✓ std::cout works" << std::endl;
    std::cerr << "✓ std::cerr works" << std::endl;
    
    // 测试 printf
    printf("✓ printf works\n");
    
    std::cout << "Basic output test completed" << std::endl;
}

void TestLoggerOutput() {
    std::cout << "\n=== Testing Logger Output ===" << std::endl;
    
    // 初始化日志系统
    std::cout << "Initializing logger..." << std::endl;
    logging::Logger::Instance().Initialize("", logging::LogLevel::TRACE);
    std::cout << "Logger initialized" << std::endl;
    
    // 测试各种日志级别
    LOG_TRACE("TRACE: This is a trace message");
    LOG_DEBUG("DEBUG: This is a debug message");
    LOG_INFO("INFO: This is an info message");
    LOG_WARN("WARN: This is a warning message");
    LOG_ERROR("ERROR: This is an error message");
    LOG_CRITICAL("CRITICAL: This is a critical message");
    
    std::cout << "Logger output test completed" << std::endl;
}

void TestFormattedOutput() {
    std::cout << "\n=== Testing Formatted Output ===" << std::endl;
    
    int number = 42;
    double pi = 3.14159;
    std::string text = "CLion";
    
    LOG_INFO("Integer: {}", number);
    LOG_INFO("Double: {:.2f}", pi);
    LOG_INFO("String: {}", text);
    LOG_INFO("Mixed: {} + {} = {:.1f}", number, pi, number + pi);
    
    std::cout << "Formatted output test completed" << std::endl;
}

void TestChineseOutput() {
    std::cout << "\n=== Testing Chinese Characters ===" << std::endl;
    
    LOG_INFO("中文测试: 这是一条中文日志消息");
    LOG_WARN("警告: 这是一条中文警告消息");
    LOG_ERROR("错误: 这是一条中文错误消息");
    
    std::cout << "Chinese output test completed" << std::endl;
}

void TestLogLevels() {
    std::cout << "\n=== Testing Log Level Changes ===" << std::endl;
    
    LOG_INFO("Current log level: {}", static_cast<int>(logging::Logger::Instance().GetLevel()));
    
    // 设置为 WARN 级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::WARN);
    LOG_INFO("Setting log level to WARN");
    
    LOG_DEBUG("This DEBUG message should NOT appear");
    LOG_INFO("This INFO message should NOT appear");
    LOG_WARN("This WARN message SHOULD appear");
    LOG_ERROR("This ERROR message SHOULD appear");
    
    // 恢复到 INFO 级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::INFO);
    LOG_INFO("Log level restored to INFO");
    
    std::cout << "Log level test completed" << std::endl;
}

void TestHighFrequencyLogs() {
    std::cout << "\n=== Testing High Frequency Logs ===" << std::endl;
    
    LOG_INFO("Starting high frequency test...");
    
    for (int i = 0; i < 5; ++i) {
        LOG_DEBUG("Processing item #{}: value = {}", i + 1, i * 10);
        
        // 短暂延迟以便观察输出
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG_INFO("High frequency test completed");
    std::cout << "High frequency test completed" << std::endl;
}

void TestErrorScenarios() {
    std::cout << "\n=== Testing Error Scenarios ===" << std::endl;
    
    try {
        LOG_ERROR("Simulated error: File not found");
        LOG_ERROR("Error code: {}, Message: {}", 404, "Resource not available");
        
        // 模拟异常
        throw std::runtime_error("Test exception for logging");
        
    } catch (const std::exception& e) {
        LOG_CRITICAL("Exception caught: {}", e.what());
        LOG_ERROR("Error handling completed");
    }
    
    std::cout << "Error scenario test completed" << std::endl;
}

void TestFlushAndTiming() {
    std::cout << "\n=== Testing Flush and Timing ===" << std::endl;
    
    LOG_INFO("Message before flush");
    logging::Logger::Instance().Flush();
    std::cout << "Manual flush completed" << std::endl;
    
    LOG_INFO("Message after flush");
    
    // 测试时间戳
    LOG_INFO("First timestamp message");
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    LOG_INFO("Second timestamp message (50ms later)");
    
    std::cout << "Flush and timing test completed" << std::endl;
}

class TestClass {
public:
    void TestMethodLogging() {
        LOG_INFO("This is a log message from TestClass::TestMethodLogging");
        LOG_DEBUG("Debug info from class method");
        
        ProcessData(100);
        ProcessData(-1);
    }
    
private:
    void ProcessData(int value) {
        LOG_INFO("Processing data: {}", value);
        
        if (value < 0) {
            LOG_ERROR("Invalid value: {}", value);
        } else {
            LOG_DEBUG("Value is valid: {}", value);
        }
    }
};

int main() {
    std::cout << "=== CLion Console Logger Test ===" << std::endl;
    std::cout << "All output should appear in CLion's Run window" << std::endl;
    std::cout << "No separate console window should open" << std::endl;
    
    // 运行各种测试
    TestBasicOutput();
    TestLoggerOutput();
    TestFormattedOutput();
    TestChineseOutput();
    TestLogLevels();
    TestHighFrequencyLogs();
    TestErrorScenarios();
    TestFlushAndTiming();
    
    // 测试类中的日志
    std::cout << "\n=== Testing Class Method Logging ===" << std::endl;
    TestClass test_obj;
    test_obj.TestMethodLogging();
    std::cout << "Class method logging test completed" << std::endl;
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "✓ If you can see log messages above with timestamps and colors" << std::endl;
    std::cout << "✓ If Chinese characters display correctly" << std::endl;
    std::cout << "✓ If formatted numbers and strings work" << std::endl;
    std::cout << "✓ If different log levels show different colors" << std::endl;
    std::cout << "✓ If location info [file:line:function] appears" << std::endl;
    std::cout << "Then the CLion console logging is working perfectly!" << std::endl;
    
    LOG_INFO("CLion console test completed successfully!");
    
    return 0;
}

/*
编译说明：

在 CMakeLists.txt 中添加：

add_executable(test_clion_console
    logging/test_clion_console.cpp
)

target_link_libraries(test_clion_console PRIVATE
    logging
)

# 确保是控制台应用程序（不是 Windows 应用程序）
if(WIN32)
    set_target_properties(test_clion_console PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif()

运行方法：
1. 在 CLion 中构建项目
2. 在 CLion 中运行 test_clion_console
3. 所有输出都会显示在 CLion 的 Run 窗口中
4. 不会弹出额外的控制台窗口

预期结果：
- 在 CLion 的 Run 窗口中看到彩色的日志输出
- 时间戳和位置信息正确显示
- 中文字符正常显示
- 不同级别的日志有不同的颜色
*/
