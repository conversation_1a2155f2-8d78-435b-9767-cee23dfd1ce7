# AiLayout 日志系统

基于 spdlog 的日志系统封装，支持显示代码行数、文件名和函数名。

## 特性

- ✅ 自动显示文件名、行号和函数名
- ✅ 支持多种日志级别（TRACE, DEBUG, INFO, WARN, ERROR, CRITICAL）
- ✅ 同时输出到控制台和文件
- ✅ 支持日志文件轮转
- ✅ 线程安全
- ✅ 高性能（基于 spdlog）
- ✅ 支持格式化输出（类似 printf）
- ✅ 单例模式，全局访问

## 快速开始

### 1. 初始化日志系统

```cpp
#include "logging/logger.h"

int main() {
    // 只输出到控制台
    logging::Logger::Instance().Initialize();
    
    // 或者同时输出到文件
    logging::Logger::Instance().Initialize("logs/app.log", logging::LogLevel::DEBUG);
    
    return 0;
}
```

### 2. 使用日志宏

```cpp
#include "logging/logger.h"

void SomeFunction() {
    LOG_INFO("Application started");
    LOG_DEBUG("Debug information: {}", 42);
    LOG_WARN("Warning: parameter value is {}", "invalid");
    LOG_ERROR("Error occurred: {}", error_message);
    LOG_CRITICAL("Critical error!");
}
```

### 3. 输出格式

日志输出包含以下信息：
```
[2024-01-15 10:30:45.123] [info] [12345] [main.cpp:25:main] Application started
 ^时间戳                   ^级别  ^线程ID ^文件:行号:函数名    ^消息内容
```

## API 参考

### 日志级别

```cpp
enum class LogLevel {
    TRACE = 0,      // 最详细的信息
    DEBUG = 1,      // 调试信息
    INFO = 2,       // 一般信息
    WARN = 3,       // 警告信息
    ERROR = 4,      // 错误信息
    CRITICAL = 5,   // 严重错误
    OFF = 6         // 关闭日志
};
```

### 初始化参数

```cpp
void Initialize(const std::string& log_file_path = "",     // 日志文件路径
               LogLevel level = LogLevel::INFO,            // 日志级别
               size_t max_file_size = 1024 * 1024 * 10,   // 单文件最大大小(10MB)
               size_t max_files = 3);                     // 最大文件数量
```

### 日志宏

```cpp
LOG_TRACE(format, ...)     // 跟踪级别
LOG_DEBUG(format, ...)     // 调试级别
LOG_INFO(format, ...)      // 信息级别
LOG_WARN(format, ...)      // 警告级别
LOG_ERROR(format, ...)     // 错误级别
LOG_CRITICAL(format, ...)  // 严重错误级别
```

### 其他方法

```cpp
// 动态设置日志级别
logging::Logger::Instance().SetLevel(logging::LogLevel::DEBUG);

// 获取当前日志级别
LogLevel level = logging::Logger::Instance().GetLevel();

// 手动刷新日志缓冲区
logging::Logger::Instance().Flush();
```

## 使用示例

### 基本使用

```cpp
#include "logging/logger.h"

int main() {
    // 初始化日志系统
    logging::Logger::Instance().Initialize("logs/app.log", logging::LogLevel::DEBUG);
    
    LOG_INFO("Application started");
    
    int user_id = 12345;
    std::string username = "john_doe";
    
    LOG_DEBUG("User login: ID={}, Name={}", user_id, username);
    
    if (user_id < 0) {
        LOG_ERROR("Invalid user ID: {}", user_id);
    }
    
    LOG_INFO("Application finished");
    return 0;
}
```

### 在类中使用

```cpp
class DatabaseManager {
public:
    bool Connect(const std::string& connection_string) {
        LOG_INFO("Connecting to database: {}", connection_string);
        
        // 模拟连接逻辑
        bool success = DoConnect(connection_string);
        
        if (success) {
            LOG_INFO("Database connection established");
        } else {
            LOG_ERROR("Failed to connect to database");
        }
        
        return success;
    }
    
private:
    bool DoConnect(const std::string& conn_str) {
        LOG_DEBUG("Attempting connection with: {}", conn_str);
        // 实际连接逻辑
        return true;
    }
};
```

### 错误处理

```cpp
void ProcessFile(const std::string& filename) {
    LOG_INFO("Processing file: {}", filename);
    
    try {
        // 文件处理逻辑
        LOG_DEBUG("File opened successfully");
        
        // ... 处理逻辑 ...
        
        LOG_INFO("File processed successfully");
    } catch (const std::exception& e) {
        LOG_ERROR("Error processing file {}: {}", filename, e.what());
        throw;
    }
}
```

## 集成到现有项目

### 1. 在 CMakeLists.txt 中添加依赖

```cmake
# 在你的目标中链接 logging 库
target_link_libraries(your_target PRIVATE
    logging
)
```

### 2. 在应用程序启动时初始化

```cpp
// 在 main 函数开始处
int main() {
    // 初始化日志系统
    logging::Logger::Instance().Initialize("logs/ailayout.log", logging::LogLevel::INFO);
    
    LOG_INFO("AiLayout application started");
    
    // 你的应用程序逻辑...
    
    LOG_INFO("AiLayout application finished");
    return 0;
}
```

### 3. 替换现有的日志调用

将现有的 `std::cout`、`printf` 等调用替换为日志宏：

```cpp
// 替换前
std::cout << "User " << username << " logged in" << std::endl;

// 替换后
LOG_INFO("User {} logged in", username);
```

## 性能考虑

- 日志系统基于 spdlog，具有很高的性能
- 使用异步日志可以进一步提升性能（如需要可以扩展）
- WARN 级别及以上的日志会自动刷新到磁盘
- 低级别日志（TRACE, DEBUG）在 Release 模式下可以通过编译时宏来禁用

## MSVC 控制台输出特殊配置

### Windows 控制台支持

在 MSVC 环境下，日志系统会自动配置控制台以支持：

1. **UTF-8 编码**：正确显示中文和其他 Unicode 字符
2. **ANSI 颜色**：启用彩色日志输出
3. **控制台分配**：为 Windows 应用程序自动分配控制台窗口

### 测试程序

提供了专门的测试程序来验证 MSVC 控制台输出：

```bash
# 构建测试程序
cmake .. -DBUILD_LOGGING_TESTS=ON

# 运行测试
test_console_msvc.exe      # 完整的控制台测试
simple_console_test.exe    # 简化的控制台测试
```

### 项目配置建议

对于 MSVC 项目，建议在 CMakeLists.txt 中添加：

```cmake
if(MSVC)
    # UTF-8 编码支持
    target_compile_options(your_target PRIVATE "/utf-8")

    # 控制台应用程序设置
    set_target_properties(your_target PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )

    # 预处理器定义
    target_compile_definitions(your_target PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
endif()
```

## 注意事项

1. 确保在使用日志宏之前调用 `Initialize()`
2. 日志文件会自动轮转，避免单个文件过大
3. 线程安全，可以在多线程环境中使用
4. 格式化字符串使用 fmt 库语法，不是 printf 语法
5. **MSVC 特殊注意**：
   - 确保项目配置为控制台应用程序或正确分配控制台
   - 使用 UTF-8 编码编译选项 `/utf-8`
   - 在 Visual Studio Developer Command Prompt 中运行以获得最佳效果
