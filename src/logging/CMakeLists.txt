project(logging)

# 查找 spdlog 包
find_package(spdlog CONFIG REQUIRED)

# 创建静态库
add_library(${PROJECT_NAME} STATIC
    logger.h
    logger.cpp
)

# 链接 spdlog
target_link_libraries(${PROJECT_NAME} PUBLIC
    spdlog::spdlog
)

# 设置包含目录
target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/../
)

# 设置 C++ 标准
target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_14)

# 如果是 MSVC，添加 UTF-8 编码支持和控制台相关设置
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE "/utf-8")
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
endif()

# 可选：构建测试程序
option(BUILD_LOGGING_TESTS "Build logging test programs" OFF)

if(BUILD_LOGGING_TESTS)
    # 基本测试程序
    add_executable(test_logger
        test_logger.cpp
    )

    target_link_libraries(test_logger PRIVATE
        ${PROJECT_NAME}
    )

    # MSVC 控制台测试程序
    add_executable(test_console_msvc
        test_console_msvc.cpp
    )

    target_link_libraries(test_console_msvc PRIVATE
        ${PROJECT_NAME}
    )

    # 确保测试程序是控制台应用程序
    if(WIN32)
        set_target_properties(test_logger test_console_msvc PROPERTIES
            LINK_FLAGS "/SUBSYSTEM:CONSOLE"
        )
    endif()

    # 使用示例程序
    add_executable(logging_example
        example.cpp
    )

    target_link_libraries(logging_example PRIVATE
        ${PROJECT_NAME}
    )

    if(WIN32)
        set_target_properties(logging_example PROPERTIES
            LINK_FLAGS "/SUBSYSTEM:CONSOLE"
        )
    endif()
endif()
