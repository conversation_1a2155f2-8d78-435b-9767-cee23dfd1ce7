project(logging)

# 查找 spdlog 包
find_package(spdlog CONFIG REQUIRED)

# 创建静态库
add_library(${PROJECT_NAME} STATIC
        logger.h
        logger.cpp
)

# 链接 spdlog
target_link_libraries(${PROJECT_NAME} PUBLIC
        spdlog::spdlog
)

# 设置包含目录
target_include_directories(${PROJECT_NAME} PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/../
)

# 设置 C++ 标准
target_compile_features(${PROJECT_NAME} PUBLIC cxx_std_14)

# 如果是 MSVC，添加 UTF-8 编码支持和控制台相关设置
if (MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE "/utf-8")
    target_compile_definitions(${PROJECT_NAME} PRIVATE
            _CRT_SECURE_NO_WARNINGS
            NOMINMAX
            WIN32_LEAN_AND_MEAN
    )
endif ()
