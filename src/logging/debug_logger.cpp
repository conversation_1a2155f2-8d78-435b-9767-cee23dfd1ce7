#include "logging/logger.h"
#include <iostream>

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * @brief 调试日志输出问题的测试程序
 */

void TestDirectSpdlog() {
    std::cout << "\n=== Testing Direct spdlog ===" << std::endl;
    
    // 直接使用 spdlog 测试
    auto console = spdlog::stdout_color_mt("console");
    console->info("Direct spdlog test message");
    console->warn("Direct spdlog warning");
    console->error("Direct spdlog error");
    
    std::cout << "Direct spdlog test completed" << std::endl;
}

void TestBasicOutput() {
    std::cout << "\n=== Testing Basic Output ===" << std::endl;
    
    // 测试基本输出
    std::cout << "std::cout test" << std::endl;
    std::cerr << "std::cerr test" << std::endl;
    printf("printf test\n");
    
    std::cout << "Basic output test completed" << std::endl;
}

void TestLoggerInitialization() {
    std::cout << "\n=== Testing Logger Initialization ===" << std::endl;
    
    try {
        // 初始化日志系统
        std::cout << "Calling Logger::Initialize..." << std::endl;
        logging::Logger::Instance().Initialize("", logging::LogLevel::TRACE);
        std::cout << "Logger::Initialize completed" << std::endl;
        
        // 检查初始化状态
        auto level = logging::Logger::Instance().GetLevel();
        std::cout << "Current log level: " << static_cast<int>(level) << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during initialization: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception during initialization" << std::endl;
    }
}

void TestLogMacros() {
    std::cout << "\n=== Testing Log Macros ===" << std::endl;
    
    try {
        std::cout << "Testing LOG_INFO..." << std::endl;
        LOG_INFO("This is an INFO message");
        
        std::cout << "Testing LOG_WARN..." << std::endl;
        LOG_WARN("This is a WARN message");
        
        std::cout << "Testing LOG_ERROR..." << std::endl;
        LOG_ERROR("This is an ERROR message");
        
        std::cout << "Testing LOG_DEBUG..." << std::endl;
        LOG_DEBUG("This is a DEBUG message");
        
        std::cout << "Testing LOG_TRACE..." << std::endl;
        LOG_TRACE("This is a TRACE message");
        
        std::cout << "Testing LOG_CRITICAL..." << std::endl;
        LOG_CRITICAL("This is a CRITICAL message");
        
        std::cout << "Log macro tests completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during log macro test: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception during log macro test" << std::endl;
    }
}

void TestFormattedLogs() {
    std::cout << "\n=== Testing Formatted Logs ===" << std::endl;
    
    try {
        int number = 42;
        double pi = 3.14159;
        std::string text = "Hello";
        
        LOG_INFO("Formatted integer: {}", number);
        LOG_INFO("Formatted double: {:.2f}", pi);
        LOG_INFO("Formatted string: {}", text);
        LOG_INFO("Multiple params: {} + {} = {}", number, pi, number + pi);
        
        std::cout << "Formatted log tests completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during formatted log test: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception during formatted log test" << std::endl;
    }
}

void TestFlush() {
    std::cout << "\n=== Testing Flush ===" << std::endl;
    
    try {
        LOG_INFO("Message before flush");
        logging::Logger::Instance().Flush();
        std::cout << "Flush completed" << std::endl;
        LOG_INFO("Message after flush");
        
    } catch (const std::exception& e) {
        std::cerr << "Exception during flush test: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown exception during flush test" << std::endl;
    }
}

#ifdef _WIN32
void PrintWindowsConsoleInfo() {
    std::cout << "\n=== Windows Console Information ===" << std::endl;
    
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hConsole != INVALID_HANDLE_VALUE) {
        CONSOLE_SCREEN_BUFFER_INFO csbi;
        if (GetConsoleScreenBufferInfo(hConsole, &csbi)) {
            std::cout << "Console buffer size: " << csbi.dwSize.X << " x " << csbi.dwSize.Y << std::endl;
            std::cout << "Console window size: " 
                      << csbi.srWindow.Right - csbi.srWindow.Left + 1 
                      << " x " << csbi.srWindow.Bottom - csbi.srWindow.Top + 1 << std::endl;
        }
        
        DWORD mode;
        if (GetConsoleMode(hConsole, &mode)) {
            std::cout << "Console mode: 0x" << std::hex << mode << std::dec << std::endl;
            std::cout << "Virtual terminal processing: " 
                      << ((mode & ENABLE_VIRTUAL_TERMINAL_PROCESSING) ? "Enabled" : "Disabled") << std::endl;
        }
    }
    
    std::cout << "Console output code page: " << GetConsoleOutputCP() << std::endl;
    std::cout << "Console input code page: " << GetConsoleCP() << std::endl;
    
    HWND consoleWindow = GetConsoleWindow();
    std::cout << "Console window handle: " << consoleWindow << std::endl;
}
#endif

int main() {
    std::cout << "=== Logger Debug Program ===" << std::endl;
    std::cout << "This program will help diagnose logger output issues" << std::endl;

#ifdef _WIN32
    // 设置控制台标题
    SetConsoleTitle(L"Logger Debug Test");
    PrintWindowsConsoleInfo();
#endif

    // 测试基本输出
    TestBasicOutput();
    
    // 测试直接 spdlog
    TestDirectSpdlog();
    
    // 测试日志器初始化
    TestLoggerInitialization();
    
    // 测试日志宏
    TestLogMacros();
    
    // 测试格式化日志
    TestFormattedLogs();
    
    // 测试刷新
    TestFlush();
    
    std::cout << "\n=== Debug Test Summary ===" << std::endl;
    std::cout << "1. Check if you can see 'Direct spdlog test message' above" << std::endl;
    std::cout << "2. Check if you can see 'This is an INFO message' above" << std::endl;
    std::cout << "3. Check if you can see formatted messages with numbers" << std::endl;
    std::cout << "4. If you can't see log messages, there might be an initialization issue" << std::endl;
    
#ifdef _WIN32
    std::cout << "\nPress any key to continue..." << std::endl;
    system("pause");
#else
    std::cout << "\nPress Enter to continue..." << std::endl;
    std::cin.get();
#endif
    
    return 0;
}

/*
编译说明：
1. 确保链接了 spdlog 库
2. 确保项目配置为控制台应用程序
3. 在 MSVC 中使用 /utf-8 编译选项

CMake 配置：
add_executable(debug_logger debug_logger.cpp)
target_link_libraries(debug_logger PRIVATE logging)
if(WIN32)
    set_target_properties(debug_logger PROPERTIES LINK_FLAGS "/SUBSYSTEM:CONSOLE")
endif()
*/
