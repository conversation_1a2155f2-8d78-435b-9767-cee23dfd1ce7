#include "logging/logger.h"
#include <iostream>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

/**
 * @brief 简单的控制台输出测试程序
 * 专门用于验证 MSVC 环境下的控制台输出是否正常
 */

int main() {
    // 设置控制台标题
#ifdef _WIN32
    SetConsoleTitle(L"Logger Console Test - MSVC");
#endif

    std::cout << "=== Simple Console Test for MSVC ===" << std::endl;
    std::cout << "Testing basic console output..." << std::endl;

    // 初始化日志系统
    std::cout << "Initializing logger..." << std::endl;
    logging::Logger::Instance().Initialize("", logging::LogLevel::TRACE);
    
    std::cout << "Logger initialized. Testing log output:" << std::endl;
    std::cout << std::endl;

    // 测试各种日志级别
    LOG_TRACE("TRACE: This is a trace message");
    LOG_DEBUG("DEBUG: This is a debug message");
    LOG_INFO("INFO: This is an info message");
    LOG_WARN("WARN: This is a warning message");
    LOG_ERROR("ERROR: This is an error message");
    LOG_CRITICAL("CRITICAL: This is a critical message");

    std::cout << std::endl;
    std::cout << "Testing formatted output:" << std::endl;

    // 测试格式化输出
    int number = 42;
    double pi = 3.14159;
    std::string name = "MSVC Test";

    LOG_INFO("Number: {}", number);
    LOG_INFO("Pi: {:.2f}", pi);
    LOG_INFO("Name: {}", name);
    LOG_INFO("Combined: {} + {} = {:.1f}", number, pi, number + pi);

    std::cout << std::endl;
    std::cout << "Testing Chinese characters:" << std::endl;
    
    // 测试中文输出
    LOG_INFO("中文测试: 这是一条中文日志消息");
    LOG_WARN("警告: 这是一条中文警告消息");
    LOG_ERROR("错误: 这是一条中文错误消息");

    std::cout << std::endl;
    std::cout << "Testing log level changes:" << std::endl;

    // 测试日志级别变化
    logging::Logger::Instance().SetLevel(logging::LogLevel::WARN);
    LOG_DEBUG("This debug message should NOT appear");
    LOG_INFO("This info message should NOT appear");
    LOG_WARN("This warning message SHOULD appear");
    LOG_ERROR("This error message SHOULD appear");

    // 恢复日志级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::INFO);
    LOG_INFO("Log level restored to INFO");

    std::cout << std::endl;
    std::cout << "=== Console Test Summary ===" << std::endl;
    std::cout << "If you can see:" << std::endl;
    std::cout << "1. ✓ Colored log messages (different colors for different levels)" << std::endl;
    std::cout << "2. ✓ Proper Chinese characters (not garbled)" << std::endl;
    std::cout << "3. ✓ Timestamps and location info [file:line:function]" << std::endl;
    std::cout << "4. ✓ Formatted numbers and strings" << std::endl;
    std::cout << "Then the MSVC console output is working correctly!" << std::endl;

    std::cout << std::endl;
    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();

    return 0;
}

/*
编译说明：

1. 使用 Visual Studio Developer Command Prompt
2. 确保项目配置为控制台应用程序
3. 编译命令示例：
   cl /EHsc /std:c++14 /I"path\to\spdlog\include" simple_console_test.cpp logger.cpp /link spdlog.lib

或者使用 CMake：
add_executable(simple_console_test simple_console_test.cpp)
target_link_libraries(simple_console_test PRIVATE logging)
set_target_properties(simple_console_test PROPERTIES LINK_FLAGS "/SUBSYSTEM:CONSOLE")

预期输出：
- 不同颜色的日志消息
- 正确显示的中文字符
- 格式化的数字和字符串
- 时间戳和位置信息
*/
