#include "logging/logger.h"
#include <iostream>
#include <thread>
#include <chrono>

/**
 * @brief 演示日志系统使用的示例函数
 */
void DemoFunction() {
    LOG_INFO("This is an info message from DemoFunction");
    LOG_WARN("This is a warning message with parameter: {}", 42);
    LOG_ERROR("This is an error message with multiple parameters: {} and {}", "string", 3.14);
}

/**
 * @brief 另一个演示函数
 */
void AnotherFunction() {
    LOG_DEBUG("Debug message from AnotherFunction");
    LOG_TRACE("Trace message with data: {}", "some data");
}

/**
 * @brief 模拟一些业务逻辑
 */
class BusinessLogic {
public:
    void ProcessData(int id) {
        LOG_INFO("Processing data with ID: {}", id);
        
        if (id < 0) {
            LOG_ERROR("Invalid ID: {}, must be non-negative", id);
            return;
        }
        
        if (id > 1000) {
            LOG_WARN("Large ID detected: {}, this might cause performance issues", id);
        }
        
        // 模拟处理时间
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        LOG_DEBUG("Data processing completed for ID: {}", id);
    }
    
    void HandleError() {
        LOG_CRITICAL("Critical error occurred in BusinessLogic!");
    }
};

/**
 * @brief 日志系统使用示例
 */
int main() {
    std::cout << "=== Logger Example ===" << std::endl;
    
    // 1. 初始化日志系统（只输出到控制台）
    logging::Logger::Instance().Initialize("", logging::LogLevel::TRACE);
    
    LOG_INFO("Logger example started");
    
    // 2. 基本日志记录
    LOG_TRACE("This is a trace message");
    LOG_DEBUG("This is a debug message");
    LOG_INFO("This is an info message");
    LOG_WARN("This is a warning message");
    LOG_ERROR("This is an error message");
    LOG_CRITICAL("This is a critical message");
    
    // 3. 带参数的日志记录
    std::string name = "AiLayout";
    int version = 1;
    double pi = 3.14159;
    
    LOG_INFO("Application: {}, Version: {}, Pi: {:.2f}", name, version, pi);
    
    // 4. 在函数中使用日志
    DemoFunction();
    AnotherFunction();
    
    // 5. 在类中使用日志
    BusinessLogic business;
    business.ProcessData(100);
    business.ProcessData(-1);
    business.ProcessData(1500);
    business.HandleError();
    
    // 6. 动态改变日志级别
    LOG_INFO("Changing log level to WARN");
    logging::Logger::Instance().SetLevel(logging::LogLevel::WARN);
    
    LOG_DEBUG("This debug message will not be shown");
    LOG_INFO("This info message will not be shown");
    LOG_WARN("This warning message will be shown");
    LOG_ERROR("This error message will be shown");
    
    // 7. 恢复日志级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::INFO);
    LOG_INFO("Log level restored to INFO");
    
    // 8. 手动刷新日志
    logging::Logger::Instance().Flush();
    
    LOG_INFO("Logger example completed");
    
    return 0;
}

/*
编译和运行示例：

1. 确保项目已经配置了 spdlog 依赖
2. 在 CMakeLists.txt 中添加示例可执行文件（可选）：

add_executable(logging_example
    logging/example.cpp
)

target_link_libraries(logging_example PRIVATE
    logging
)

3. 编译项目
4. 运行示例程序

预期输出格式：
[2024-01-15 10:30:45.123] [info] [12345] [example.cpp:45:main] Logger example started
[2024-01-15 10:30:45.124] [trace] [12345] [example.cpp:48:main] This is a trace message
[2024-01-15 10:30:45.125] [debug] [12345] [example.cpp:49:main] This is a debug message
...
*/
