#include "logger.h"
#include <spdlog/fmt/fmt.h>
#include <iostream>

namespace logging {

Logger& Logger::Instance() {
    static Logger instance;
    return instance;
}

void Logger::Initialize(const std::string& log_file_path, LogLevel level, 
                       size_t max_file_size, size_t max_files) {
    try {
        std::vector<spdlog::sink_ptr> sinks;

        // 添加控制台输出 sink
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(ToSpdlogLevel(level));
        console_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%t] %v");
        sinks.push_back(console_sink);

        // 如果指定了日志文件路径，添加文件输出 sink
        if (!log_file_path.empty()) {
            auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                log_file_path, max_file_size, max_files);
            file_sink->set_level(ToSpdlogLevel(level));
            file_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");
            sinks.push_back(file_sink);
        }

        // 创建 logger
        logger_ = std::make_shared<spdlog::logger>("ailayout_logger", sinks.begin(), sinks.end());
        logger_->set_level(ToSpdlogLevel(level));
        logger_->flush_on(spdlog::level::warn);  // WARN 级别及以上自动刷新

        // 注册为默认 logger
        spdlog::register_logger(logger_);
        spdlog::set_default_logger(logger_);

        current_level_ = level;
        initialized_ = true;

        LOG_INFO("Logger initialized successfully. Level: {}, File: {}", 
                static_cast<int>(level), log_file_path.empty() ? "console only" : log_file_path);

    } catch (const spdlog::spdlog_ex& ex) {
        std::cerr << "Log initialization failed: " << ex.what() << std::endl;
        initialized_ = false;
    }
}

void Logger::SetLevel(LogLevel level) {
    if (logger_) {
        logger_->set_level(ToSpdlogLevel(level));
        current_level_ = level;
        LOG_INFO("Log level changed to: {}", static_cast<int>(level));
    }
}

LogLevel Logger::GetLevel() const {
    return current_level_;
}

void Logger::Flush() {
    if (logger_) {
        logger_->flush();
    }
}

spdlog::level::level_enum Logger::ToSpdlogLevel(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE:
            return spdlog::level::trace;
        case LogLevel::DEBUG:
            return spdlog::level::debug;
        case LogLevel::INFO:
            return spdlog::level::info;
        case LogLevel::WARN:
            return spdlog::level::warn;
        case LogLevel::ERROR:
            return spdlog::level::err;
        case LogLevel::CRITICAL:
            return spdlog::level::critical;
        case LogLevel::OFF:
            return spdlog::level::off;
        default:
            return spdlog::level::info;
    }
}

} // namespace logging
