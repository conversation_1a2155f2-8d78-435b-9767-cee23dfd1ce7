#include "logging/logger.h"
#include <iostream>
#include <string>

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * @brief 测试 MSVC 控制台输出的专用程序
 */

void TestConsoleOutput() {
    std::cout << "\n=== Testing Console Output in MSVC ===" << std::endl;
    
    // 测试基本输出
    std::cout << "Standard cout output" << std::endl;
    std::cerr << "Standard cerr output" << std::endl;
    
    // 测试日志输出
    LOG_TRACE("TRACE: This should appear in trace color");
    LOG_DEBUG("DEBUG: This should appear in debug color");
    LOG_INFO("INFO: This should appear in info color");
    LOG_WARN("WARN: This should appear in warning color");
    LOG_ERROR("ERROR: This should appear in error color");
    LOG_CRITICAL("CRITICAL: This should appear in critical color");
}

void TestUnicodeOutput() {
    std::cout << "\n=== Testing Unicode Output ===" << std::endl;
    
    LOG_INFO("Testing Chinese characters: 中文测试");
    LOG_INFO("Testing Japanese characters: 日本語テスト");
    LOG_INFO("Testing Korean characters: 한국어 테스트");
    LOG_INFO("Testing special symbols: ★☆♠♣♥♦");
    LOG_INFO("Testing emojis: 😀😃😄😁");
}

void TestFormattedOutput() {
    std::cout << "\n=== Testing Formatted Output ===" << std::endl;
    
    int number = 42;
    double pi = 3.14159;
    std::string text = "Hello World";
    
    LOG_INFO("Integer formatting: {}", number);
    LOG_INFO("Float formatting: {:.2f}", pi);
    LOG_INFO("String formatting: {}", text);
    LOG_INFO("Mixed formatting: {} + {} = {:.1f}", number, pi, number + pi);
    LOG_INFO("Hex formatting: 0x{:X}", number);
    LOG_INFO("Binary formatting: 0b{:b}", number);
}

void TestLongMessages() {
    std::cout << "\n=== Testing Long Messages ===" << std::endl;
    
    std::string long_message = "This is a very long log message that should test the console output capabilities. ";
    long_message += "It contains multiple sentences and should wrap properly in the console window. ";
    long_message += "We want to make sure that even very long messages are displayed correctly without truncation or corruption.";
    
    LOG_INFO("Long message test: {}", long_message);
    
    // 测试多行消息
    LOG_INFO("Multi-line message:\nLine 1: First line\nLine 2: Second line\nLine 3: Third line");
}

void TestHighFrequencyOutput() {
    std::cout << "\n=== Testing High Frequency Output ===" << std::endl;
    
    LOG_INFO("Starting high frequency test...");
    
    for (int i = 0; i < 10; ++i) {
        LOG_DEBUG("High frequency message #{}: Processing item {}", i + 1, i * 100);
        
        // 模拟一些处理时间
        #ifdef _WIN32
        Sleep(50);  // 50ms
        #else
        usleep(50000);  // 50ms
        #endif
    }
    
    LOG_INFO("High frequency test completed");
}

void TestErrorScenarios() {
    std::cout << "\n=== Testing Error Scenarios ===" << std::endl;
    
    try {
        LOG_ERROR("Simulated error: File not found");
        LOG_ERROR("Error code: {}, Message: {}", 404, "Resource not available");
        
        // 模拟异常
        throw std::runtime_error("Test exception");
        
    } catch (const std::exception& e) {
        LOG_CRITICAL("Exception caught: {}", e.what());
        LOG_ERROR("Stack trace would go here...");
    }
}

#ifdef _WIN32
void PrintConsoleInfo() {
    std::cout << "\n=== Console Information ===" << std::endl;
    
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hConsole != INVALID_HANDLE_VALUE) {
        CONSOLE_SCREEN_BUFFER_INFO csbi;
        if (GetConsoleScreenBufferInfo(hConsole, &csbi)) {
            std::cout << "Console size: " << csbi.srWindow.Right - csbi.srWindow.Left + 1 
                      << " x " << csbi.srWindow.Bottom - csbi.srWindow.Top + 1 << std::endl;
        }
        
        DWORD mode;
        if (GetConsoleMode(hConsole, &mode)) {
            std::cout << "Console mode: 0x" << std::hex << mode << std::dec << std::endl;
            std::cout << "Virtual terminal processing: " 
                      << ((mode & ENABLE_VIRTUAL_TERMINAL_PROCESSING) ? "Enabled" : "Disabled") << std::endl;
        }
    }
    
    std::cout << "Console output code page: " << GetConsoleOutputCP() << std::endl;
    std::cout << "Console input code page: " << GetConsoleCP() << std::endl;
}
#endif

int main() {
    std::cout << "=== MSVC Console Output Test ===" << std::endl;
    std::cout << "Testing spdlog console output in MSVC environment" << std::endl;
    
#ifdef _WIN32
    PrintConsoleInfo();
#endif
    
    // 初始化日志系统 - 只输出到控制台
    logging::Logger::Instance().Initialize("", logging::LogLevel::TRACE);
    
    LOG_INFO("Logger initialized for console output test");
    LOG_INFO("Compiler: {}", 
#ifdef _MSC_VER
        "MSVC"
#elif defined(__GNUC__)
        "GCC"
#elif defined(__clang__)
        "Clang"
#else
        "Unknown"
#endif
    );
    
    // 运行各种测试
    TestConsoleOutput();
    TestUnicodeOutput();
    TestFormattedOutput();
    TestLongMessages();
    TestHighFrequencyOutput();
    TestErrorScenarios();
    
    // 测试日志级别变化
    std::cout << "\n=== Testing Log Level Changes ===" << std::endl;
    LOG_INFO("Changing log level to ERROR");
    logging::Logger::Instance().SetLevel(logging::LogLevel::ERROR);
    
    LOG_DEBUG("This DEBUG message should NOT appear");
    LOG_INFO("This INFO message should NOT appear");
    LOG_WARN("This WARN message should NOT appear");
    LOG_ERROR("This ERROR message SHOULD appear");
    LOG_CRITICAL("This CRITICAL message SHOULD appear");
    
    // 恢复日志级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::INFO);
    LOG_INFO("Log level restored to INFO");
    
    // 手动刷新
    LOG_INFO("Flushing logs...");
    logging::Logger::Instance().Flush();
    
    LOG_INFO("Console output test completed successfully!");
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "If you can see:" << std::endl;
    std::cout << "1. Colored log messages" << std::endl;
    std::cout << "2. Proper Unicode characters" << std::endl;
    std::cout << "3. Correct timestamps and location info" << std::endl;
    std::cout << "4. No garbled text or encoding issues" << std::endl;
    std::cout << "Then the MSVC console output is working correctly!" << std::endl;
    
#ifdef _WIN32
    std::cout << "\nPress any key to continue..." << std::endl;
    system("pause");
#endif
    
    return 0;
}

/*
编译说明：

对于 MSVC：
1. 确保项目配置为 Console Application
2. 在项目属性中设置：
   - Character Set: Use Unicode Character Set
   - C/C++ -> Advanced -> Compile As: Compile as C++ Code (/TP)

对于 CMake：
add_executable(test_console_msvc
    logging/test_console_msvc.cpp
)

target_link_libraries(test_console_msvc PRIVATE
    logging
)

# 如果是 Windows 子系统应用，设置为控制台应用
if(WIN32)
    set_target_properties(test_console_msvc PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:CONSOLE"
    )
endif()
*/
