#include "logging/logger.h"
#include <iostream>

void TestBasicLogging() {
    std::cout << "\n=== Testing Basic Logging ===" << std::endl;
    
    LOG_TRACE("This is a TRACE message");
    LOG_DEBUG("This is a DEBUG message");
    LOG_INFO("This is an INFO message");
    LOG_WARN("This is a WARN message");
    LOG_ERROR("This is an ERROR message");
    LOG_CRITICAL("This is a CRITICAL message");
}

void TestFormattedLogging() {
    std::cout << "\n=== Testing Formatted Logging ===" << std::endl;
    
    int number = 42;
    double pi = 3.14159;
    std::string name = "AiLayout";
    
    LOG_INFO("Integer: {}", number);
    LOG_INFO("Double: {:.2f}", pi);
    LOG_INFO("String: {}", name);
    LOG_INFO("Multiple params: {} + {} = {}", 10, 20, 30);
    LOG_INFO("Mixed types: {}, {:.1f}, {}", name, pi, number);
}

void TestLogLevels() {
    std::cout << "\n=== Testing Log Levels ===" << std::endl;
    
    LOG_INFO("Current log level: {}", static_cast<int>(logging::Logger::Instance().GetLevel()));
    
    // 设置为 WARN 级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::WARN);
    LOG_INFO("Setting log level to WARN");
    
    LOG_DEBUG("This DEBUG message should NOT appear");
    LOG_INFO("This INFO message should NOT appear");
    LOG_WARN("This WARN message SHOULD appear");
    LOG_ERROR("This ERROR message SHOULD appear");
    
    // 恢复到 DEBUG 级别
    logging::Logger::Instance().SetLevel(logging::LogLevel::DEBUG);
    LOG_INFO("Log level restored to DEBUG");
}

class TestClass {
public:
    void TestMethod() {
        LOG_INFO("This is a log message from TestClass::TestMethod");
        LOG_DEBUG("Debug info from class method");
        
        ProcessData(100);
        ProcessData(-1);
    }
    
private:
    void ProcessData(int value) {
        LOG_INFO("Processing data: {}", value);
        
        if (value < 0) {
            LOG_ERROR("Invalid value: {}", value);
        } else {
            LOG_DEBUG("Value is valid: {}", value);
        }
    }
};

int main() {
    std::cout << "=== Logger Test Program ===" << std::endl;
    
    // 初始化日志系统 - 只输出到控制台，级别为 DEBUG
    logging::Logger::Instance().Initialize("", logging::LogLevel::DEBUG);
    
    LOG_INFO("Logger test program started");
    
    // 测试基本日志功能
    TestBasicLogging();
    
    // 测试格式化日志
    TestFormattedLogging();
    
    // 测试日志级别
    TestLogLevels();
    
    // 测试类中的日志
    std::cout << "\n=== Testing Class Logging ===" << std::endl;
    TestClass test_obj;
    test_obj.TestMethod();
    
    // 测试手动刷新
    std::cout << "\n=== Testing Manual Flush ===" << std::endl;
    LOG_INFO("Before flush");
    logging::Logger::Instance().Flush();
    LOG_INFO("After flush");
    
    LOG_INFO("Logger test program completed");
    
    std::cout << "\n=== Test Completed ===" << std::endl;
    std::cout << "If you can see log messages with file names, line numbers, and function names," << std::endl;
    std::cout << "then the logger is working correctly!" << std::endl;
    
    return 0;
}

/*
编译和运行测试：

1. 在 logging/CMakeLists.txt 中添加测试可执行文件：

add_executable(test_logger
    test_logger.cpp
)

target_link_libraries(test_logger PRIVATE
    logging
)

2. 编译项目
3. 运行 test_logger

预期输出应该包含类似以下格式的日志：
[2024-01-15 10:30:45.123] [info] [12345] [test_logger.cpp:85:main] Logger test program started
[2024-01-15 10:30:45.124] [trace] [12345] [test_logger.cpp:8:TestBasicLogging] This is a TRACE message
...
*/
