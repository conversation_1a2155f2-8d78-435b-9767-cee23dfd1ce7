#include <QApplication>
#include <QFile>
#include <QPushButton>
#include <QTextStream>
#include <QDir>

#include "main_window.h"
#include "logging/logger.h"

int main(int argc, char* argv[]) {
  QApplication a(argc, argv);

  // QFile styleFile(":/style.qss");
  // if (styleFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
  //     QTextStream ts(&styleFile);
  //     a.setStyleSheet(ts.readAll());
  //     styleFile.close();
  // }
  logging::Logger::Instance().Initialize();

  LOG_INFO("Creating main window");
  MainWindows w;
  w.resize(1200, 800);
  w.show();

  LOG_INFO("Main window shown, entering event loop");
  int result = a.exec();

  LOG_INFO("Application finished with exit code: {}", result);
  return result;
}