#ifndef APP_AGENT_LLM_RULE_AGENT
#define APP_AGENT_LLM_RULE_AGENT

#include <QJsonObject>
#include <QMutex>
#include "mq/mq_client.h"

namespace agent {

class LlmRuleAgent {
public:
  LlmRuleAgent();
  ~LlmRuleAgent();

  QJsonObject GetNearRule(const QStringList& comp_list);
  void SetSchImagePath(const QString& image_path);
  bool CheckSchImagePath();
  QJsonObject TextToRule(const QString& text);
  QJsonObject GetRules(const QStringList& comp_list, const QString& text,
                       const QStringList& operators);

private:
  void* context_;
  void* socket_;
  MqClient mq_client_;
  QString sch_image_path_;
  QMutex mutex_;
  void BuildMqRequest(const QString& opt, const QString& image_path,
                      const QStringList& comp_list, QString& buf);
  void BuildMqRequest(const QStringList& opts, const QString& image_path,
                      const QStringList& comp_list, const QString& text,
                      QString& buf);
  void ImageToBase64(const QString& image_path, QString& base64);

};

} // namespace Agent

#endif  // !APP_AGENT_LLM_RULE_AGENT
