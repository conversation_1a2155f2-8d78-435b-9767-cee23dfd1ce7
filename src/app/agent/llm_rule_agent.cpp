#include "llm_rule_agent.h"

#include <QBuffer>
#include <QFile>
#include <QImage>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMutexLocker>

#include <QMessageBox>

namespace agent {
const QString Task = "task";
const QString Image = "base64";
const QString ComponentList = "netlist";
const QString ManualRules = "manual_rules";
const QString NearRule = "PROXIMITY";
const QString TextRule = "text_to_analysis";

LlmRuleAgent::LlmRuleAgent() {
  if (!mq_client_.ConnectReq("tcp://10.0.20.173:5555")) {
    QMessageBox::warning(nullptr, "Warning",
                         mq_client_.LastErrorMessage().data());
  }
  mq_client_.SetReqSendTimeout(10000);
}

LlmRuleAgent::~LlmRuleAgent() {
  mq_client_.DisconnectReq();
}

QJsonObject LlmRuleAgent::GetNearRule(const QStringList& comp_list) {
  QMutexLocker locker(&mutex_);
  QString buf;
  BuildMqRequest(NearRule, sch_image_path_, comp_list, buf);
  std::string reply;
  if (mq_client_.Request(buf.toStdString().c_str(), buf.size(), reply)) {
    return QJsonDocument::fromJson(QByteArray(reply.data(), reply.size())).
        object();
  }
  return {};
}

void LlmRuleAgent::SetSchImagePath(const QString& image_path) {
  sch_image_path_ = image_path;
}

bool LlmRuleAgent::CheckSchImagePath() {
  if (sch_image_path_.isEmpty()) {
    return false;
  }
  if (!QFile::exists(sch_image_path_)) {
    return false;
  }
  return true;
}

void LlmRuleAgent::BuildMqRequest(const QString& opt, const QString& image_path,
                                  const QStringList& comp_list, QString& buf) {
  QJsonObject root;
  root[Task] = opt;

  QString base64;
  ImageToBase64(image_path, base64);
  root[Image] = base64;

  QJsonArray comp_array;
  for (auto& comp_name : comp_list) {
    comp_array.append(comp_name);
  }
  root[ComponentList] = std::move(comp_array);

  QJsonDocument doc(root);
  buf = doc.toJson(QJsonDocument::Compact);
}

void LlmRuleAgent::BuildMqRequest(const QStringList& opts,
                                  const QString& image_path,
                                  const QStringList& comp_list,
                                  const QString& text, QString& buf) {
  QJsonObject root;
  root[Task] = QJsonArray::fromStringList(opts);

  QString base64;
  ImageToBase64(image_path, base64);
  root[Image] = base64;
  root[ComponentList] = QJsonArray::fromStringList(comp_list);
  root[ManualRules] = text;

  QJsonDocument doc(root);
  buf = doc.toJson(QJsonDocument::Compact);
}

void LlmRuleAgent::ImageToBase64(const QString& image_path, QString& base64) {
  QImage image(image_path);
  QByteArray ba;
  QBuffer buffer(&ba);
  buffer.open(QIODevice::WriteOnly);
  image.save(&buffer, "PNG");
  base64 = ba.toBase64();
}

QJsonObject LlmRuleAgent::TextToRule(const QString& text) {
  QMutexLocker locker(&mutex_);
  QJsonObject root;
  root[Task] = TextRule;
  root[text] = text;

  QJsonDocument doc(root);
  auto buf = doc.toJson(QJsonDocument::Compact);

  std::string reply;
  if (mq_client_.Request(buf.toStdString().c_str(), buf.size(), reply)) {
    return QJsonDocument::fromJson(QByteArray(reply.data(), reply.size())).
        object();
  }
  return {};
}

QJsonObject LlmRuleAgent::GetRules(const QStringList& comp_list,
                                   const QString& text,
                                   const QStringList& operators) {

  QString buf;
  BuildMqRequest(operators, sch_image_path_, comp_list, text, buf);
  std::string reply;
  if (mq_client_.Request(buf.toStdString().c_str(), buf.size(), reply)) {
    return QJsonDocument::fromJson(QByteArray(reply.data(), reply.size())).
        object();
  }
  return {};
}
} // namespace Agent