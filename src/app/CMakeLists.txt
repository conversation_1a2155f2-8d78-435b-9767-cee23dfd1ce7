find_package(Qt5 COMPONENTS Widgets Gui Core Concurrent REQUIRED)
find_package(ZeroMQ CONFIG REQUIRED)

set(RESOURCES ../resources/resources.qrc)
qt5_add_resources(RESOURCES_RCC ${RESOURCES})

include_directories(${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/../)

add_executable(${PROJECT_NAME} WIN32
        main.cpp
        main_window.h
        main_window.cpp
        ui/log_widget.h
        ui/log_widget.cpp
        ui/project_widget.h
        ui/project_widget.cpp
        ui/rule_config_widget.h
        ui/rule_config_widget.cpp
        ui/rule_config_dialog.h
        ui/rule_config_dialog.cpp
        agent/llm_rule_agent.h
        agent/llm_rule_agent.cpp
        ${RESOURCES_RCC}
)

target_link_libraries(${PROJECT_NAME} PRIVATE
        Qt5::Widgets
        Qt5::Gui
        Qt5::Core
        Qt5::Concurrent
        editor
        mq
)
