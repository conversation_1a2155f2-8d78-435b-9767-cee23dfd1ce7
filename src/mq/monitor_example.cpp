// 使用示例：如何在您的代码中使用 zmq_socket_monitor 功能

#include "mq_client.h"
#include <iostream>
#include <thread>
#include <chrono>

void example_basic_monitoring() {
    std::cout << "=== 基本监控示例 ===" << std::endl;
    
    MqClient client;
    
    // 1. 首先连接
    if (!client.ConnectReq("tcp://127.0.0.1:5555")) {
        std::cout << "连接失败: " << client.LastErrorMessage() << std::endl;
        return;
    }
    
    // 2. 启用监控
    if (!client.EnableReqMonitoring()) {
        std::cout << "启用监控失败: " << client.LastErrorMessage() << std::endl;
        return;
    }
    
    std::cout << "监控已启用，等待连接事件..." << std::endl;
    
    // 3. 等待连接建立
    if (client.WaitForReqConnection(5000)) {
        std::cout << "连接成功建立！" << std::endl;
    } else {
        std::cout << "连接超时或失败" << std::endl;
    }
    
    // 4. 检查真实连接状态
    if (client.IsReqReallyConnected()) {
        std::cout << "当前已真正连接" << std::endl;
    } else {
        std::cout << "当前未真正连接" << std::endl;
    }
}

void example_event_monitoring() {
    std::cout << "\n=== 事件监控示例 ===" << std::endl;
    
    MqClient client;
    
    // 启用监控
    if (!client.EnableReqMonitoring()) {
        std::cout << "启用监控失败" << std::endl;
        return;
    }
    
    // 连接到服务器
    if (!client.ConnectReq("tcp://127.0.0.1:5555")) {
        std::cout << "连接失败: " << client.LastErrorMessage() << std::endl;
        return;
    }
    
    // 持续监控事件
    std::cout << "开始监控连接事件..." << std::endl;
    
    for (int i = 0; i < 50; ++i) {  // 监控5秒
        std::string endpoint;
        auto event = client.CheckReqConnectionEvent(&endpoint);
        
        switch (event) {
            case MqClient::EVENT_CONNECTED:
                std::cout << "✓ 连接已建立到: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_CONNECT_DELAYED:
                std::cout << "⏳ 连接延迟: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_CONNECT_RETRIED:
                std::cout << "🔄 连接重试: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_DISCONNECTED:
                std::cout << "❌ 连接断开: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_HANDSHAKE_SUCCEEDED:
                std::cout << "🤝 握手成功: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_HANDSHAKE_FAILED:
                std::cout << "💥 握手失败: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_ERROR:
                std::cout << "⚠️ 监控错误" << std::endl;
                break;
            case MqClient::EVENT_NONE:
                // 没有事件，继续等待
                break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "监控结束" << std::endl;
}

// 在 LlmRuleAgent 中的使用示例
void example_llm_agent_usage() {
    std::cout << "\n=== LlmRuleAgent 使用示例 ===" << std::endl;
    
    MqClient mq_client;
    
    // 启用监控
    if (!mq_client.EnableReqMonitoring()) {
        std::cout << "Failed to enable connection monitoring" << std::endl;
        return;
    }
    
    // 连接到服务器
    if (!mq_client.ConnectReq("tcp://10.0.20.173:55575")) {
        std::cout << "连接失败: " << mq_client.LastErrorMessage() << std::endl;
        return;
    }
    
    // 等待真正的连接建立
    std::cout << "等待连接建立..." << std::endl;
    if (mq_client.WaitForReqConnection(5000)) {
        std::cout << "连接建立成功！" << std::endl;
    } else {
        std::cout << "连接超时或失败！" << std::endl;
        return;
    }
    
    // 设置超时
    mq_client.SetReqSendTimeout(10000);
    
    // 在发送请求前检查连接状态
    if (!mq_client.IsReqReallyConnected()) {
        std::cout << "连接未建立，无法发送请求" << std::endl;
        return;
    }
    
    // 发送测试请求
    std::string test_request = R"({"task": "test", "message": "hello"})";
    std::string reply;
    
    if (mq_client.Request(test_request.c_str(), test_request.size(), reply)) {
        std::cout << "请求成功，回复: " << reply << std::endl;
    } else {
        std::cout << "请求失败: " << mq_client.LastErrorMessage() << std::endl;
        
        // 检查是否是连接问题
        if (!mq_client.IsReqReallyConnected()) {
            std::cout << "连接已断开" << std::endl;
        }
    }
}

int main() {
    std::cout << "ZMQ Socket Monitor 使用示例" << std::endl;
    std::cout << "================================" << std::endl;
    
    // 运行示例（注意：需要有对应的服务器运行）
    example_basic_monitoring();
    example_event_monitoring();
    example_llm_agent_usage();
    
    return 0;
}
