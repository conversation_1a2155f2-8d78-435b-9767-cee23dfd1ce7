// 测试 zmq_socket_monitor 功能的简单程序

#include "mq_client.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    std::cout << "测试 ZMQ Socket Monitor 功能" << std::endl;
    std::cout << "==============================" << std::endl;
    
    MqClient client;
    
    std::cout << "1. 启用监控..." << std::endl;
    if (!client.EnableReqMonitoring()) {
        std::cout << "❌ 启用监控失败: " << client.LastErrorMessage() << std::endl;
        return 1;
    }
    std::cout << "✓ 监控已启用" << std::endl;
    
    std::cout << "\n2. 尝试连接到不存在的服务器..." << std::endl;
    if (!client.ConnectReq("tcp://127.0.0.1:9999")) {
        std::cout << "❌ 连接失败: " << client.LastErrorMessage() << std::endl;
    } else {
        std::cout << "✓ zmq_connect 调用成功（但这不意味着真正连接）" << std::endl;
    }
    
    std::cout << "\n3. 检查连接状态..." << std::endl;
    std::cout << "HasReq(): " << (client.HasReq() ? "true" : "false") << std::endl;
    std::cout << "IsReqReallyConnected(): " << (client.IsReqReallyConnected() ? "true" : "false") << std::endl;
    
    std::cout << "\n4. 监控连接事件（10秒）..." << std::endl;
    auto start_time = std::chrono::steady_clock::now();
    
    while (true) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - start_time);
        if (elapsed.count() >= 10) {
            break;
        }
        
        std::string endpoint;
        auto event = client.CheckReqConnectionEvent(&endpoint);
        
        switch (event) {
            case MqClient::EVENT_CONNECTED:
                std::cout << "✓ 连接已建立到: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_CONNECT_DELAYED:
                std::cout << "⏳ 连接延迟: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_CONNECT_RETRIED:
                std::cout << "🔄 连接重试: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_DISCONNECTED:
                std::cout << "❌ 连接断开: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_HANDSHAKE_SUCCEEDED:
                std::cout << "🤝 握手成功: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_HANDSHAKE_FAILED:
                std::cout << "💥 握手失败: " << endpoint << std::endl;
                break;
            case MqClient::EVENT_ERROR:
                std::cout << "⚠️ 监控错误" << std::endl;
                break;
            case MqClient::EVENT_NONE:
                // 没有事件，继续等待
                break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "\n5. 最终连接状态检查..." << std::endl;
    std::cout << "IsReqReallyConnected(): " << (client.IsReqReallyConnected() ? "true" : "false") << std::endl;
    
    std::cout << "\n6. 测试等待连接（5秒超时）..." << std::endl;
    if (client.WaitForReqConnection(5000)) {
        std::cout << "✓ 连接建立成功" << std::endl;
    } else {
        std::cout << "❌ 连接超时或失败" << std::endl;
    }
    
    std::cout << "\n测试完成！" << std::endl;
    std::cout << "\n说明：" << std::endl;
    std::cout << "- zmq_connect() 只检查格式，总是立即返回成功" << std::endl;
    std::cout << "- HasReq() 只检查 socket 是否存在和 zmq_connect 是否调用过" << std::endl;
    std::cout << "- IsReqReallyConnected() 通过监控真正的连接事件来判断" << std::endl;
    std::cout << "- 连接到不存在的服务器会看到重试事件，但永远不会真正连接" << std::endl;
    
    return 0;
}
