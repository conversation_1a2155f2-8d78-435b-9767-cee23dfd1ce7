# ZeroMQ Socket Monitor 功能说明

## 概述

本实现为 `MqClient` 类添加了基于 `zmq_socket_monitor` 的真正连接状态监控功能，解决了 `zmq_connect` 只检查格式而不等待真正连接建立的问题。

## 问题背景

- `zmq_connect()` 只验证端点格式，格式正确就立即返回成功
- 真正的网络连接建立是异步进行的
- `ZMQ_EVENTS` 只能检查 I/O 就绪状态，不能检测连接状态
- 需要一种方法来检测真正的连接建立和断开

## 解决方案

使用 `zmq_socket_monitor` 创建监控 socket 来接收连接事件：

### 新增的 API

#### 连接事件枚举
```cpp
enum ConnectionEvent {
    EVENT_NONE = 0,              // 无事件
    EVENT_CONNECTED = 1,         // 连接已建立
    EVENT_CONNECT_DELAYED = 2,   // 连接延迟
    EVENT_CONNECT_RETRIED = 3,   // 连接重试
    EVENT_DISCONNECTED = 4,      // 连接断开
    EVENT_HANDSHAKE_SUCCEEDED = 5, // 握手成功
    EVENT_HANDSHAKE_FAILED = 6,  // 握手失败
    EVENT_ERROR = -1             // 错误
};
```

#### 监控控制方法
```cpp
bool EnableReqMonitoring();                    // 启用监控
void DisableReqMonitoring();                   // 禁用监控
bool IsReqMonitoringEnabled() const;           // 检查是否启用监控
```

#### 连接状态检查方法
```cpp
ConnectionEvent CheckReqConnectionEvent(std::string* endpoint = nullptr);  // 非阻塞检查事件
bool WaitForReqConnection(int timeout_ms = 5000);                         // 等待连接建立
bool IsReqReallyConnected();                                              // 检查真实连接状态
```

## 使用方法

### 1. 基本使用流程

```cpp
MqClient client;

// 1. 启用监控
if (!client.EnableReqMonitoring()) {
    std::cout << "启用监控失败" << std::endl;
    return;
}

// 2. 连接
if (!client.ConnectReq("tcp://127.0.0.1:5555")) {
    std::cout << "连接失败" << std::endl;
    return;
}

// 3. 等待真正连接建立
if (client.WaitForReqConnection(5000)) {
    std::cout << "连接建立成功！" << std::endl;
} else {
    std::cout << "连接超时" << std::endl;
}
```

### 2. 事件监控

```cpp
// 非阻塞检查连接事件
std::string endpoint;
auto event = client.CheckReqConnectionEvent(&endpoint);

switch (event) {
    case MqClient::EVENT_CONNECTED:
        std::cout << "连接已建立到: " << endpoint << std::endl;
        break;
    case MqClient::EVENT_DISCONNECTED:
        std::cout << "连接断开: " << endpoint << std::endl;
        break;
    // ... 处理其他事件
}
```

### 3. 在 LlmRuleAgent 中的使用

```cpp
LlmRuleAgent::LlmRuleAgent() {
    // 启用监控
    if (!mq_client_.EnableReqMonitoring()) {
        QMessageBox::warning(nullptr, "Warning", "Failed to enable monitoring");
    }
    
    // 连接
    if (!mq_client_.ConnectReq("tcp://10.0.20.173:55575")) {
        QMessageBox::warning(nullptr, "Warning", mq_client_.LastErrorMessage().data());
    } else {
        // 等待真正连接建立
        if (mq_client_.WaitForReqConnection(5000)) {
            qDebug() << "Connection established!";
        } else {
            QMessageBox::warning(nullptr, "Warning", "Connection timeout!");
        }
    }
}

QJsonObject LlmRuleAgent::GetNearRule(const QStringList& comp_list) {
    // 检查真实连接状态
    if (!mq_client_.IsReqReallyConnected()) {
        qDebug() << "Connection not established";
        return {};
    }
    
    // 发送请求...
}
```

## API 对比

| 方法 | 检查内容 | 返回时机 |
|------|----------|----------|
| `zmq_connect()` | 端点格式 | 立即返回 |
| `HasReq()` | socket 存在 + zmq_connect 调用过 | 立即返回 |
| `IsReqReallyConnected()` | 真正的网络连接状态 | 基于监控事件 |

## 注意事项

1. **监控开销**: 监控功能会创建额外的 PAIR socket，有一定开销
2. **事件处理**: 需要定期调用 `CheckReqConnectionEvent()` 或 `IsReqReallyConnected()` 来处理事件
3. **线程安全**: 监控方法不是线程安全的，需要外部同步
4. **资源清理**: 析构函数会自动清理监控资源

## 测试

编译并运行测试程序：

```bash
cd build
make test_monitor
./test_monitor
```

这将演示：
- 连接到不存在服务器的行为
- 监控事件的接收
- 真实连接状态 vs 表面连接状态的区别

## 优势

1. **真正的连接检测**: 能够检测到真正的网络连接建立和断开
2. **事件驱动**: 可以异步接收连接状态变化通知
3. **向后兼容**: 保持了原有 API 的兼容性
4. **详细信息**: 提供连接失败原因、重试信息等详细状态

这个实现解决了 ZeroMQ 连接状态检测的根本问题，让您能够准确知道连接的真实状态！
