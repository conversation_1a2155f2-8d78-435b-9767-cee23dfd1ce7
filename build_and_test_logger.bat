@echo off
echo ===================================
echo Building and Testing Logger System
echo ===================================

REM 创建构建目录
if not exist "build" mkdir build
cd build

echo.
echo Configuring CMake...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Debug

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Generated files:
dir /b program\bin\*.exe 2>nul

echo.
echo You can now run the application:
echo   program\bin\AiLayout.exe
echo.
echo Log files will be created in:
echo   logs\ailayout.log
echo.

pause
