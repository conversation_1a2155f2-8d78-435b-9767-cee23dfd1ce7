@echo off
chcp 65001 >nul
echo ========================================
echo Quick Console Output Test for MSVC
echo ========================================
echo.

REM 检查是否有构建目录
if not exist "build_msvc" (
    echo Build directory not found. Please run build_msvc_console_test.bat first.
    pause
    exit /b 1
)

cd build_msvc

REM 检查测试程序是否存在
if not exist "Debug\simple_console_test.exe" (
    echo simple_console_test.exe not found.
    echo Building test program...
    
    cmake --build . --config Debug --target simple_console_test
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to build test program.
        pause
        exit /b 1
    )
)

echo Running simple console test...
echo ========================================
echo.

REM 运行简单控制台测试
Debug\simple_console_test.exe

echo.
echo ========================================
echo Test completed.
echo.
echo If you saw colored log messages and proper Chinese characters,
echo then the MSVC console output is working correctly!
echo.

pause
