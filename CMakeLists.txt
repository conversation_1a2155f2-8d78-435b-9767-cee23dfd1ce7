cmake_minimum_required(VERSION 3.18)
project(AiLayout)

if (${CMAKE_TOOLCHAIN_FILE} EQUAL "")
    set(VCPKG_ROOT "C:/Users/<USER>/.vcpkg-clion/vcpkg")
    set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")
    set(CMAKE_PREFIX_PATH ${CMAKE_PREFIX_PATH} "${VCPKG_ROOT}/installed/x64-windows")
endif ()

set(QT_PATH "C:/Qt/5.15.2/msvc2019_64")
set(CMAKE_PREFIX_PATH ${CMAKE_PREFIX_PATH} ${QT_PATH})
set(CMAKE_CXX_STANDARD 14)

if (MSVC)
    add_compile_options("/utf-8")
endif ()

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/program/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/program/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/program/bin)

add_subdirectory(src)

add_custom_target(deploy
        COMMAND ${QT_PATH}/bin/windeployqt.exe
        ${CMAKE_CURRENT_LIST_DIR}/program/bin/${PROJECT_NAME}.exe
)