# AiLayout 日志系统实现总结

## 概述

已成功在 `src/logging` 目录中实现了一个基于 spdlog 的日志系统，能够显示代码行数、文件名和函数名。

## 创建的文件

### 核心文件
1. **`src/logging/logger.h`** - 日志系统头文件
   - 定义了 Logger 单例类
   - 提供了便捷的日志宏（LOG_INFO, LOG_ERROR 等）
   - 支持格式化输出和位置信息显示

2. **`src/logging/logger.cpp`** - 日志系统实现文件
   - 实现了日志初始化、级别设置等功能
   - 支持同时输出到控制台和文件
   - 支持日志文件轮转

3. **`src/logging/CMakeLists.txt`** - 构建配置
   - 链接 spdlog 库
   - 配置编译选项

### 文档和示例
4. **`src/logging/README.md`** - 详细使用文档
5. **`src/logging/example.cpp`** - 使用示例
6. **`src/logging/test_logger.cpp`** - 测试程序

### 构建脚本
7. **`build_and_test_logger.bat`** - Windows 构建脚本

## 主要特性

### ✅ 自动显示位置信息
- 文件名（去掉路径）
- 行号
- 函数名

### ✅ 多种日志级别
- TRACE, DEBUG, INFO, WARN, ERROR, CRITICAL

### ✅ 格式化输出
- 支持 fmt 库语法
- 类型安全的参数传递

### ✅ 多输出目标
- 控制台输出（带颜色）
- 文件输出（支持轮转）

### ✅ 高性能
- 基于 spdlog，性能优异
- 线程安全

## 使用方法

### 1. 初始化（在 main 函数中）
```cpp
#include "logging/logger.h"

int main() {
    // 初始化日志系统
    logging::Logger::Instance().Initialize("logs/app.log", logging::LogLevel::INFO);
    
    LOG_INFO("Application started");
    // ... 应用程序逻辑
    return 0;
}
```

### 2. 在代码中使用
```cpp
#include "logging/logger.h"

void SomeFunction() {
    LOG_INFO("Function called");
    LOG_DEBUG("Debug info: {}", 42);
    LOG_WARN("Warning: {}", "something");
    LOG_ERROR("Error occurred: {}", error_msg);
}
```

### 3. 输出格式
```
[2024-01-15 10:30:45.123] [info] [12345] [main.cpp:25:main] Application started
 ^时间戳                   ^级别  ^线程ID ^文件:行号:函数名    ^消息内容
```

## 项目集成

### 已更新的文件
1. **`src/CMakeLists.txt`** - 添加了 logging 子目录
2. **`src/app/CMakeLists.txt`** - 链接了 logging 库
3. **`src/app/main.cpp`** - 集成了日志系统初始化
4. **`src/app/main_window.cpp`** - 添加了日志示例

### 依赖关系
- 项目已在 `vcpkg.json` 中包含 spdlog 依赖
- logging 库被正确链接到主应用程序

## 构建和测试

### 使用提供的脚本
```bash
# Windows
build_and_test_logger.bat

# 或手动构建
mkdir build && cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

### 运行应用程序
```bash
program/bin/AiLayout.exe
```

### 查看日志
- 控制台：实时显示日志
- 文件：`logs/ailayout.log`

## 日志级别说明

| 级别 | 用途 | 示例 |
|------|------|------|
| TRACE | 最详细的调试信息 | 函数进入/退出 |
| DEBUG | 调试信息 | 变量值、状态变化 |
| INFO | 一般信息 | 应用启动、重要操作 |
| WARN | 警告信息 | 非致命错误、性能问题 |
| ERROR | 错误信息 | 操作失败、异常 |
| CRITICAL | 严重错误 | 系统崩溃、致命错误 |

## 性能考虑

1. **编译时优化**：可以通过宏定义在 Release 模式下禁用低级别日志
2. **异步日志**：如需要可以扩展为异步模式
3. **文件轮转**：自动管理日志文件大小，避免磁盘空间问题

## 扩展建议

### 可能的改进
1. **异步日志**：对于高频日志场景
2. **网络日志**：发送到远程日志服务器
3. **结构化日志**：JSON 格式输出
4. **日志过滤**：基于模块或标签的过滤

### 集成建议
1. 在所有关键函数入口添加 LOG_DEBUG
2. 在错误处理中添加 LOG_ERROR
3. 在重要状态变化时添加 LOG_INFO
4. 在性能敏感的地方使用 LOG_TRACE

## 注意事项

1. **初始化顺序**：确保在使用日志宏前调用 Initialize()
2. **字符串转换**：QString 需要调用 .toStdString() 转换
3. **性能影响**：虽然 spdlog 很快，但仍要避免在紧密循环中使用高频日志
4. **线程安全**：日志系统是线程安全的，可以在多线程环境中使用

## 总结

日志系统已成功集成到 AiLayout 项目中，提供了：
- 完整的位置信息显示（文件名:行号:函数名）
- 多级别日志支持
- 高性能和线程安全
- 易于使用的宏接口
- 完善的文档和示例

现在可以在整个项目中使用这个日志系统来改善调试和监控能力。
