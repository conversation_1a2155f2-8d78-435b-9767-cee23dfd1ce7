@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Building Logger System for MSVC Console
echo ========================================

REM 检查是否安装了 Visual Studio
where cl.exe >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: MSVC compiler (cl.exe) not found in PATH
    echo Please run this script from a Visual Studio Developer Command Prompt
    echo or run vcvarsall.bat first
    pause
    exit /b 1
)

REM 显示编译器信息
echo.
echo Compiler Information:
cl.exe 2>&1 | findstr "Microsoft"

REM 创建构建目录
if not exist "build_msvc" mkdir build_msvc
cd build_msvc

echo.
echo Configuring CMake for MSVC...
cmake .. -G "Visual Studio 16 2019" -A x64 ^
    -DCMAKE_BUILD_TYPE=Debug ^
    -DBUILD_LOGGING_TESTS=ON ^
    -DCMAKE_INSTALL_PREFIX=install

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Debug --parallel

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM 检查生成的可执行文件
echo Generated test programs:
if exist "Debug\test_console_msvc.exe" (
    echo   ✓ test_console_msvc.exe
) else (
    echo   ✗ test_console_msvc.exe (not found)
)

if exist "Debug\test_logger.exe" (
    echo   ✓ test_logger.exe
) else (
    echo   ✗ test_logger.exe (not found)
)

if exist "Debug\logging_example.exe" (
    echo   ✓ logging_example.exe
) else (
    echo   ✗ logging_example.exe (not found)
)

if exist "src\app\Debug\AiLayout.exe" (
    echo   ✓ AiLayout.exe
) else (
    echo   ✗ AiLayout.exe (not found)
)

echo.
echo ========================================
echo Testing Console Output
echo ========================================

if exist "Debug\test_console_msvc.exe" (
    echo Running MSVC console test...
    echo.
    Debug\test_console_msvc.exe
    echo.
    echo MSVC console test completed.
) else (
    echo test_console_msvc.exe not found, skipping test.
)

echo.
echo ========================================
echo Usage Instructions
echo ========================================
echo.
echo To run the tests manually:
echo   cd build_msvc
echo   Debug\test_console_msvc.exe    - Test MSVC console output
echo   Debug\test_logger.exe          - Test basic logging functionality
echo   Debug\logging_example.exe      - Run usage examples
echo.
echo To run the main application:
echo   src\app\Debug\AiLayout.exe
echo.
echo Log files will be created in:
echo   logs\ailayout.log
echo.

pause
