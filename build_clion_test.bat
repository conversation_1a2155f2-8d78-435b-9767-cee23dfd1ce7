@echo off
echo ========================================
echo Building Logger for CLion Console Test
echo ========================================

REM 创建构建目录
if not exist "build" mkdir build
cd build

echo.
echo Configuring CMake with logging tests enabled...
cmake .. -G "MinGW Makefiles" ^
    -DCMAKE_BUILD_TYPE=Debug ^
    -DBUILD_LOGGING_TESTS=ON

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Debug

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM 检查生成的可执行文件
echo Generated test programs:
if exist "src\logging\test_clion_console.exe" (
    echo   ✓ test_clion_console.exe
) else (
    echo   ✗ test_clion_console.exe (not found)
)

if exist "src\logging\debug_logger.exe" (
    echo   ✓ debug_logger.exe
) else (
    echo   ✗ debug_logger.exe (not found)
)

if exist "program\bin\AiLayout.exe" (
    echo   ✓ AiLayout.exe
) else (
    echo   ✗ AiLayout.exe (not found)
)

echo.
echo ========================================
echo Testing CLion Console Output
echo ========================================

if exist "src\logging\test_clion_console.exe" (
    echo Running CLion console test...
    echo This should show colored output in the current console:
    echo.
    src\logging\test_clion_console.exe
    echo.
    echo CLion console test completed.
) else (
    echo test_clion_console.exe not found, skipping test.
)

echo.
echo ========================================
echo Usage Instructions for CLion
echo ========================================
echo.
echo To use in CLion:
echo 1. Open the project in CLion
echo 2. Configure CMake with: -DBUILD_LOGGING_TESTS=ON
echo 3. Build the project
echo 4. Run 'test_clion_console' target
echo 5. Check the output in CLion's Run window
echo.
echo The main application can be run with:
echo   program\bin\AiLayout.exe
echo.
echo Log files will be created in:
echo   logs\ailayout.log
echo.

pause
